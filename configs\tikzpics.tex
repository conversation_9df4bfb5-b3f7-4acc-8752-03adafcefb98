\usepackage{tikz}

% TikZ styles for drawing
\usetikzlibrary{decorations.pathmorphing} % for snake lines
\usetikzlibrary{matrix} % for block alignment
\usetikzlibrary{shapes, arrows} % for arrow heads
\usetikzlibrary{calc, math} % for manimulation of coordinates


\tikzstyle{input} = [coordinate]
\tikzstyle{output} = [coordinate]
\tikzstyle{pinstyle} = [pin edge={to-,thick,black}]
\tikzstyle{block} = [draw,rectangle,thick,minimum height=2em,minimum width=2em]
\tikzstyle{sum} = [draw,circle,inner sep=0mm,minimum size=2mm]
\tikzstyle{connector} = [->,thick]
\tikzstyle{line} = [thick]
\tikzstyle{branch} = [circle,inner sep=0pt,minimum size=1mm,fill=black,draw=black]
\tikzstyle{guide} = []
\tikzstyle{snakeline} = [connector, decorate, decoration={pre length=0.2cm,
                         post length=0.2cm, snake, amplitude=.4mm,
                         segment length=2mm},thick, magenta, ->]
                         

% PGF Plots                        
\usepackage{pgfplots}
\pgfplotsset{width=10cm,compat=1.9}

% \usepgfplotslibrary{external}
% \tikzexternalize
% \usetikzlibrary{external}
% \tikzexternalize[prefix=tikz/]

