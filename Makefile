# LaTeX Makefile for CPE769_relatorio project
# Main document configuration
MAIN_DOC = CPE769_relatorio
TEX_FILES = $(wildcard *.tex) $(wildcard configs/*.tex)
BIB_FILES = $(wildcard *.bib)
OUTPUT_DIR = .
PDF_VIEWER = xdg-open

# Compiler settings
LATEX = pdflatex
BIBTEX = bibtex
LATEX_FLAGS = -interaction=nonstopmode -halt-on-error -output-directory=$(OUTPUT_DIR)

# Generated files to clean
AUX_FILES = *.aux *.log *.bbl *.blg *.out *.synctex.gz *.fdb_latexmk *.fls *.acr *.toc *.lof *.lot *.fdb_latexmk *.figlist *.makefile *.fls *.figlist *.makefile

.PHONY: all clean distclean cleanall view help

# Default target
all: $(MAIN_DOC).pdf

# Main compilation target with bibliography support
$(MAIN_DOC).pdf: $(TEX_FILES) $(BIB_FILES)
    @echo "Compiling $(MAIN_DOC).tex..."
    $(LATEX) $(LATEX_FLAGS) $(MAIN_DOC).tex
    @if grep -q "\\bibliography" $(MAIN_DOC).tex; then \
        echo "Running bibtex..."; \
        $(BIBTEX) $(MAIN_DOC); \
        echo "Recompiling for bibliography..."; \
        $(LATEX) $(LATEX_FLAGS) $(MAIN_DOC).tex; \
    fi
    @echo "Final compilation for cross-references..."
    $(LATEX) $(LATEX_FLAGS) $(MAIN_DOC).tex
    @echo "PDF generated successfully: $(MAIN_DOC).pdf"

# Clean auxiliary files
clean:
    @echo "Cleaning auxiliary files..."
    @rm -f $(AUX_FILES)
    @echo "Auxiliary files cleaned."

# Clean all files including PDF
distclean cleanall: clean
    @echo "Removing PDF output..."
    @rm -f $(MAIN_DOC).pdf
    @echo "All generated files removed."

# View the generated PDF
view: $(MAIN_DOC).pdf
    @echo "Opening $(MAIN_DOC).pdf..."
    @$(PDF_VIEWER) $(MAIN_DOC).pdf &

# Display help information
help:
    @echo "Available targets:"
    @echo "  all (default) - Compile $(MAIN_DOC).tex to PDF"
    @echo "  clean         - Remove auxiliary files"
    @echo "  distclean     - Remove all generated files including PDF"
    @echo "  cleanall      - Same as distclean"
    @echo "  view          - Open the generated PDF with default viewer"
    @echo "  help          - Display this help message"
    @echo ""
    @echo "Main document: $(MAIN_DOC).tex"
    @echo "PDF viewer: $(PDF_VIEWER)"