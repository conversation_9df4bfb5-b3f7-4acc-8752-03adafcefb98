\usepackage[dvipsnames,svgnames,table]{xcolor}
\usepackage{booktabs} % commands for table rules
\usepackage{tabularx, multirow, makecell, caption}
\usepackage{fancyhdr}                               %cabeçalho e rodapé estilo fancy
% \usepackage{graphicx}
% \usepackage{titleps}
% \usepackage{titletoc}
% \usepackage{lastpage}
% \usepackage{biblatex} 								%Imports biblatex package

%% Text Font and Language
% \usepackage{times}                                  %Fonte Times New Roman
% \usepackage[portuguese]{babel}						%Permite escrever ç e tals
% \usepackage[utf8]{inputenc}							%Permite escrever ç e tals

%% Diagramação páginas e Texto em geral
\usepackage{acro}    								%Permite a criação da lista de acrônimos
\usepackage{multicol}								%Permite a formação de páginas com + de 1 coluna p/ texto
\usepackage{titlesec}    							%Permite a estilização dos sections e subsections
\usepackage{float}                                  %permite fixar a posição Tabela ou Imagem no texto
\usepackage{afterpage}							    %Permite a criação de páginas em branco
% \usepackage[acronym]{glossaries}		            %Permite a criação da lista de acrônimos
%\usepackage[printonlyused,withpage]{acronym}
% \usepackage{longtable}                              %Permite a criação da lista de acrônimos tabelada
% \usepackage{tabu}                                   %Permite a criação da lista (depende de longtable)
% \usepackage{supertabular}



% \usepackage[usenames, dvipsnames]{color}			%Permite colorir os recursos do latex e criar cores
\usepackage{caption}							    %Permite alterar tamanho da fonte nos captions (Figs Tabs)
\usepackage{wrapfig}								%Permite inserir fig e tabs no meio do texto
% \usepackage{indentfirst}							%Identa o primeiro parágrafo de uma section.

%% Diagramação Tabelas
 \usepackage[unframed,autolinebreaks,useliterate]{configs/mcode} 	%Quote de código MATLAB + plot code .m
\usepackage{array}                                  %Não lembro
\usepackage{multirow}		                        %Permite pegar mais de uma linha da Tabela

%% Diagramação Figuras
\usepackage{graphicx}								%Permite se inserir gráficos
%\usepackage{tikz}			                        %Permite o uso do ambiente tikz
\usepackage{subcaption}		                        %permite criar o subcaption da subfigure
%\usepackage{subfig}
  
%% MATH
\usepackage{commath}	                        	%permite o uso de \norm{} (norma) e de \abs{} (módulo)
\usepackage{amsmath, amsfonts, amssymb, esint}	    %Escritas do modo math
\usepackage{bm} 									%Negrito para modo math greek letters
\usepackage{steinmetz}		                        %Fase /____
\usepackage{mathtools}                              %For matrix align (deixar sempre acima de mathabx para evitar erro \underbrace)
\usepackage{mathabx}		                        %convolution \Asterisk


\usepackage[compress, space]{cite}
% \usepackage{amsmath,amssymb,amsfonts}
\usepackage{algorithmic}
% \usepackage{graphicx}
\usepackage{textcomp}
% \usepackage{xcolor}
% \usepackage{acro}

\usepackage{cuted}
\usepackage{flushend}

% \usepackage[portuguese]{babel}
% \usepackage[utf8]{inputenc}
\usepackage{orcidlink}