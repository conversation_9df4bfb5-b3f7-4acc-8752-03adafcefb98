\documentclass[conference]{IEEEtran}
\IEEEoverridecommandlockouts
% The preceding line is only needed to identify funding in the first footnote. If that is unneeded, please comment it out.
\input{configs/packs}
\input{configs/acronimos}
\input{configs/tikzpics}

%% MINI MACROS
\newcommand\refF[1]{Fig. \ref{#1}}
\newcommand\refT[1]{Tab. \ref{#1}}
\newcommand\refApp[1]{Appendix \ref{#1}}


\def\BibTeX{{\rm B\kern-.05em{\sc i\kern-.025em b}\kern-.08em
    T\kern-.1667em\lower.7ex\hbox{E}\kern-.125emX}}

%% Cabeçalho e rodapé
\pagestyle{fancy}
\fancyhf{} % sets both header and footer to nothing
\renewcommand{\headrulewidth}{0pt}

\cfoot{\thepage}
\lhead{}
\chead{}
\rhead{}
\lfoot{}
\rfoot{}


\begin{document}

\title{\LARGE Aplicação de Rede Adaptativa ATC de estimativa para problema de estimar estado\\[-.3em]
\vspace{0.5cm}
\Large \textsuperscript{Trabalho referente à disciplina CPE769 - Redes Adaptativas e Sistemas Multiagente} \\ 
\Large \textsuperscript{Professor: Ricardo Merched \orcidlink{0000-0001-5280-2767}}}

\noindent

\author{ \IEEEauthorblockN{Caio C.M.P. de Alcântara}
\IEEEauthorblockA{Universidade Federal do Rio de Janeiro\\
Rio de Janeiro, RJ, Brasil \orcidlink{0009-0005-1325-0388} \\
<EMAIL>}
% \and
% \IEEEauthorblockN{Carlos R.S. Brigida}
% \IEEEauthorblockA{Universidade Federal do Rio de Janeiro\\
% Rio de Janeiro, RJ, Brasil \orcidlink{xxxx-xxxx-xxxx-xxxx} \\
% <EMAIL>}
}

% \and
% \IEEEauthorblockN{4\textsuperscript{th} Given Name Surname}
% \IEEEauthorblockA{\textit{dept. name of organization (of Aff.)} \\
% \textit{name of organization (of Aff.)}\\
% City, Country \\
% email address or ORCID}
% \and
% \IEEEauthorblockN{5\textsuperscript{th} Given Name Surname}
% \IEEEauthorblockA{\textit{dept. name of organization (of Aff.)} \\
% \textit{name of organization (of Aff.)}\\
% City, Country \\
% email address or ORCID}
% \and
% \IEEEauthorblockN{6\textsuperscript{th} Given Name Surname}
% \IEEEauthorblockA{\textit{dept. name of organization (of Aff.)} \\
% \textit{name of organization (of Aff.)}\\
% City, Country \\
% email address or ORCID}

\maketitle

% Please observe the conference page limits. = 5

%\tableofcontents

%\listoffigures

%\listoftables
%\printacronyms[include = abbrev, name = {List of Acronyms}]

%\printacronyms[include = symbol, name = {List of Symbols}]

\thispagestyle{fancy} %% Rodapé na primeira página!
\section{Introdução}

Este trabalho tem como objetivo estudar a aplicação de uma rede de sensores que cooperam para obter a melhor estimativa de determinado fenômeno observado. A ideia é utilizar os conceitos de redes adaptativas cooperativas \cite{sayed2013diffusionadaptationnetworks} para o problema de rastreamento\footnote{Futuramente, a ideia é aplicar estes conhecimentos para área de guerra acústica, mais especificamente para sistemas de SONAR ativo em Guerra Anti-Submarino: a rastrear alvos subaquáticos e monitorá-los em um cenário tático \cite{will, owen2007anti}.} \cite{Kay}.

Redes adaptativas cooperativas consistem em uma coleção de agentes com habilidades de processamento e de aprendizado \cite{sayed2013diffusionadaptationnetworks}. Nas redes de agentes, ao invés de se ter uma central que processa a informação de todos os agentes simultaneamente, os agentes são vinculados por meio de uma topologia de conexão\footnote{Formando grafos, por exemplo.} e cooperam entre si por meio de interações locais para resolver, de forma distribuída, problemas de otimização, de estimativa e de inferência em tempo real \cite{sayed2013diffusionadaptationnetworks}.

Neste trabalho, optou-se por resolver problemas de estimativa, onde há uma variável de estado corrompida por ruído \cite{Kay}, semelhante aos problemas de rastreamento de alvos utilizados em sistemas de SONAR ativo. Logo, as simulações retratam um processo estacionário cuja variável de estado é obtida a partir de uma observação ruidosa e da dinâmica de evolução de um processo \ac{iid} \cite{Kay, Baccala}, no intuito de os estimadores convergirem em probabilidade para o valor esperado \cite{Larry}. 

Cada agente da rede adaptativa possui suas próprias técnicas de filtragem. Neste trabalho, as técnicas de filtragem utilizadas pelos agentes são as de Wiener \cite{Kay, Oppenheim}, de \ac{lms} \cite{Kay, Oppenheim} e de \ac{rls} \cite{Kay, Oppenheim}. Os resultados são comparados em termos de \acs{mse} \cite{Kay, Oppenheim} e de \ac{msd} \cite{sayed2013diffusionadaptationnetworks}.

Vale ressaltar que a notação utilizada para variáveis aleatórias e operadores estatísticos neste trabalho segue o proposto por \cite{Larry}. Negrito no modo matemático irá indicar vetor para \emph{lowercase} ou matriz para \emph{uppercase}. Símbolos matemáticos, operadores, conjuntos, etc., seguem a notação de \cite{elon2020analise,neri2021analise}.

As secções que dividem este trabalho seguem a seguinte divisão: \ref{sec:conceitos} Conceitos, secção que expõe, explica e resume os principais tópicos teóricos abordados no trabalho;  \ref{sec:metodologia} Metodologia, secção que descreve o método utilizado para testar e verificar os resultados teóricos de forma computacionalmente simulada; \ref{sec:resultados} Resultados, secção destinada a descrição dos resultados gerados pelas simulações; \ref{sec:conclusoes} Conclusões, secção em que se analisam os resultados obtidos; e \ref{sec:fugure-works} Trabalhos Futuros, secção na qual se comenta as perspectivas futuras de pesquisa, de estudo e desenvolvimento.

%Sistemas de SONAR passivo são sistemas empregados por organizações militares, cujo propósito é interceptar, localizar e classificar emissores de sinal acústico que estejam no entorno de um submarino, possibilitando, identificar, rastrear potenciais agressores num teatro de operações navais \cite{owen2007anti}. A cadeia básica destes sistemas é dado pela \refF{fig:sonar_passivo_cadeia-proc}, sendo o sinal acústico captado no arranjo de hidrofones (transdutor hidroacústico) e sendo processado ao longo da cadeia até que as análises do respectivo sinal acústico sejam feitas \cite{bozzi}.
%
%\begin{figure}[!ht]
%    \centering
%    \includegraphics[scale=1.]{./images/passivo.png}
%    \caption{Cadeia básica de processamento de um Sistema de SONAR Passivo \cite{bozzi}.}
%    \label{fig:sonar_passivo_cadeia-proc}
%\end{figure}
%
%Os sinais de interesse que valem ser destacados para a análise de discriminação de contatos são o \acf{AOA} \cite{will}, a distância $r$ obtida pela análise de distância \cite{distancia_KF} e a classificação hierárquica gerada pelo sinal acústico \cite{Marlon}. Estas variáveis serão as nossas \textit{features} (atributos) de entrada $\bm{x}$ de nossa rede ART, responsável pelo algoritmo de agrupamento/clusterização.
%
%A clusterização, nesses sistemas, é um processo bastante desafiador por diversos motivos. Deve ser capaz de lidar com cenários cujas fontes emissores de sinal acústico estejam relativamente próximas e em grande quantidade, num tempo relativamente curto para não prejudicar a operação do submarino e evitar de expô-lo no cenário tático. Um desafio, por exemplo, são separar as fontes de uma esquadra que segue um determinado rumo, pois os navios (alvos) da esquadra rumam aproximadamente juntos, sendo difícil separar as fontes emissoras.
%
%A rede ART se mostra promissora para a solução deste problema, uma vez que é um algoritmo não-supervisionado, não exigindo conhecimento a priori do cenário para realizar a clusterização, o que é necessário nesta aplicação, já que não se pode prever quais e quantos emissores estarão presentes no cenário. Além disso, é estável, rápida e não tem muitos parâmetros a serem inicializados, tornando-se atrativa para este tipo de aplicação, na qual deve-se fazer uma separação relativamente rápida. Além disso, como novos neurônios são gerados a partir das próprias entradas, a rede ART é uma rede que lida bem com a detecção de novidades (e.g., seria a detecção de novos emissores de sinal acústico, distindo de outros clusters já criados, posicionados em lugares ligeiramente diferentes).

\section{Conceitos}\label{sec:conceitos}

As redes adaptativas são compostas de agentes, estes, por sua vez, são conectados uns aos outros conforme uma dada topologia. Cada agente, no caso mais geral, possui sua própria técnica de filtragem responsável por gerar a sua estimativa. No modo de rede adaptativa cooperativa, as estimativas de cada agente podem ser utilizadas em conjunto, conforme topologia da rede, no intuito de cada agente melhorar as suas próprias estimativas com médias locais das estimativas dos outros agentes na vizinhança \cite{sayed2013diffusionadaptationnetworks}. 

\subsection{Filtragem}
A ideia de "filtragem" aplicada neste trabalho é melhor mencionada em \cite[Secção 1.2.2]{Grossberg}, estando mais para o conceito de estimador \cite{Kay,Larry}, do que para o conceito de remoção de ruído \cite{Oppenheim}, dado a forma como se simulam a variável de entrada (excitação do sistema \cite{ristic2003beyond}) e a dinâmica autorregressiva que manipula a entrada \cite{Kay,Grewal}. Neste sentido, utilizam-se os algoritmos mencionados nas subsecções \ref{subsubsec:Wiener}, \ref{subsubsec:LMS} e \ref{subsubsec:RLS} abaixo.

Em suma, problemas de filtragem utilizam a minimização de uma função custo $J(\bm{w})$ no intuito de se obter a variável de interesse $\bm{w^o}$ que melhor satisfaça esta relação $J_{\textit{min}}$. A função custo usual destes estimadores é o \ac{mse}
\begin{equation}
	\text{MSE} = \mathbb{E}\left(\norm{d-\hat{y}}^2\right)
	\label{eq:funcao_custo}
\end{equation}
conforme a ideia de adaptação ilustrada no diagrama de blocos da \refF{fig:diagrama_de_blocos_simulacao}, sendo o símbolo $\hat{\cdot}$ indicativo de valor estimado\footnote{Todo valor estimado é uma variável aleatória \cite{Larry, Baccala, Kay}. Bons estimadores produzem estimativas que convergem em probabilidade para o valor desejado \cite{Larry}.} por um estimador; e $\mathbb{E}(\cdot)$, o operador esperança estatística. 

Neste sentido, os problemas de filtragem mencionados neste trabalho seguiram a lógica do diagrama de blocos dado pela \refF{fig:diagrama_de_blocos_simulacao}, sendo $u$ o processo \ac{iid} que excita o sistema (entrada); $\bm{w^o}$ o sistema desconhecido que se deseja monitorar; $y$ a observação não corrompiada; $\nu$ o ruído \ac{iid}; $d$ a observação corrompida; $\bm{\hat{w}}$ a estimativa do sistema desconhecido feita pelo estimador; $\hat{y}$ a estimativa a posteriori da observação $y$; $e$ é o erro de saída do sistema (utilizado na função custo); e $i$ é o índice temporal do sistema.
\begin{figure}[!ht]
	\centering
	\includegraphics[width=.9\columnwidth]{./images/Filtering.png}
	\caption{Problema básico de estimação de estado desconhecido. OBS: Notação compatível com \cite{sayed2013diffusionadaptationnetworks}, com excessão do vetor $\bm{u}(i)=\bm{u}_i$ e que, com excessão do $\bm{w}=\bm{w^o}$, todas as variáveis são aleatórias. OBS2: Imagem adaptada de \cite{wikipedia_lms_simu_system}.}
	\label{fig:diagrama_de_blocos_simulacao}
\end{figure}

\subsubsection{Wiener}\label{subsubsec:Wiener}
O filtro de Wiener foi secretamente proposto durante a Segunda Guerra Mundial como meio de melhorar a capacidade de baterias anti-aéreas em atingir alvos em movimento \cite{Baccala} (exemplo de aplicação de rastreamento), ou seja, o filtro sendo utilizado como elemento de estimação de posição. No domínio do tempo, o filtro de Wiener pode ser entendido como um estimador linear \cite{Kay}, onde se deseja estimar um sinal desejado $\bm{w^o}$ a partir da observação ruidosa $d$.

Na formulação da dinâmica do sistema, conforme \refF{fig:diagrama_de_blocos_simulacao}, tem-se que
\begin{equation}
	y(i) = \sum_{n=0}^{N-1} w_n(i)u(i-n)
\end{equation}
reduzindo-se para a notação matricial
\begin{equation}
	y(i) = \bm{w}^T\!(i)\bm{u}(i)
	\label{eq:y_matricial_eq}
\end{equation}
sendo $\bm{w},\bm{u}\in\mathbb{R}^N$ vetores\footnote{Por uma questão de praticidade, omitiu-se o índice temporal.} coluna e $N\in\mathbb{N}$ a ordem do sistema. Ressalta-se que $\cdot^T$ é o operador transposto.

No objetivo de se minimizar a função custo \eqref{eq:funcao_custo} \cite{Kay, Baccala}, obtem-se o algoritmo recursão \cite{diniz2020adaptive}  
\begin{equation}
	\bm{\hat{w}}^T\!(i) = \bm{\hat{w}}^T\!(i\!-\!1) - 2\mu \left(\bm{\hat{w}}^T\!(i\!-\!1) \bm{\hat{R}} - \bm{\hat{r}} + \epsilon \bm{\hat{w}}^T(i\!-\!1) \right)
\end{equation}
para estimar $\bm{w^o}$, sendo as variáveis $\bm{\hat{R}}\in\mathbb{R}^N\!\!\times\!\!\mathbb{R}^N$ e $\bm{\hat{r}}\in\mathbb{R}^N$, respectivamente, a estimativa de matriz de Riccati e a estimativa do vetor de correlação cruzada entre $y$ e $\bm{u}$ \cite{diniz2020adaptive}. Além disso, $\mu$ é o passo adaptativo e $\epsilon$ é um fator de regularização do algoritmo \cite{diniz2020adaptive}.

\subsubsection{Least Mean-Squares}\label{subsubsec:LMS}
Em suma, O algoritmo \ac{lms} é um algoritmo de filtragem adaptável utilizado em aplicações em tempo real para minimizar o \ac{mse} entre um sinal desejado e uma saída estimada.

No sentido da minimização do \ac{mse} \eqref{eq:funcao_custo} utilizando o \emph{Least Squares} \cite{Kay} para se estimar a variável de interesse, toma-se o mesmo modelo de dinâmica descrito na \refF{fig:diagrama_de_blocos_simulacao}, portanto, em notação matricial, tem-se \eqref{eq:y_matricial_eq}. Pode-se demonstrar que o algoritmo \cite{diniz2020adaptive}
\begin{equation}
	\bm{\hat{w}}(i) = \bm{\hat{w}}(i\!-\!1) + \mu\ \! e(i\!-\!1) \bm{u}(i)
\end{equation}
converge quando o passo adaptativo $\mu$ é escolhido para o intervalo $0<\mu< 2/\text{Tr}(\bm{R})< 2/\lambda_{\text{max}}$, sendo $e$ o erro de saída\footnote{Detalhe de implementação: levando-se a ideia de estimativa a priori e a posteriori \cite{Kay,ristic2003beyond}, pode-se calcular o erro da forma a priori antes de implementar o passo adaptativo, conforme $e(i|i\!-\!1)=d(i)-\bm{\hat{w}}^T\!(i\!-\!1)\bm{u}(i)$.} e $\lambda_{\text{max}}$ o maior autovalor da matriz de Riccati $\bm{R}$ \cite{diniz2020adaptive}.

Há também a forma normalizada do algoritmo \ac{lms}
\begin{equation}
	\bm{\hat{w}}(i) = \bm{\hat{w}}(i\!-\!1) + \frac{\tilde{\mu}}{\delta + \norm{\bm{u}(i)}^2} e(i\!-\!1) \bm{u}(i)
\end{equation}
sendo que, neste caso, $0<\tilde{\mu}<2$ e que $\delta>0$ é um fator para limitar o denominador a um valor maior que zero.
\subsubsection{Recursive Least Squares}\label{subsubsec:RLS}
O algoritmo \ac{rls} é uma técnica de filtragem adaptativa que minimiza a soma dos erros quadrados entre um sinal desejado e um sinal estimado, ponderando cada amostra de erro por um fator $\lambda$, $0<\lambda\leq1$, chamado de fator de esquecimento (\emph{forgetting factor}). Comparado ao algoritmo \ac{lms}, o \ac{rls} converge mais rápido, mas, por outro lado, é mais intensivo computacionalmente \cite{diniz2020adaptive}.

Dado ao fato da ponderação, o \ac{mse} fica, teoricamente falando, da forma \cite{diniz2020adaptive}
\begin{equation}
	J(\bm{w}) = \sum_{n=0}^{N}\lambda^{n}\norm{d(i-n)-\bm{w}^T\!(i)\bm{u}(i-n)}^2
\end{equation} 
que, na prática, resulta no algoritmo \cite{diniz2020adaptive,wikipedia_rls}
\begin{equation}
	\begin{array}{r@{}l}
		\hat{y}(i|i\!-\!1){\color{white}.} &= \bm{\hat{w}}^T\!(i\!-\!1)\bm{u}(i)\\
		\alpha(i) {\color{white}.}&= d(i) - \hat{y}(i|i\!-\!1)\\
		\bm{k}(i) {\color{white}.}&= \dfrac{\bm{P}(i\!-\!1) \bm{u}(i)}{\lambda + \bm{u}^T\!(i)\bm{P}(i\!-\!1)\bm{u}(i)} 
	\end{array}
	\label{eq:RLS_Prior}
\end{equation}
\begin{equation}
	\begin{array}{r@{}l}
		\bm{P}(i) {\color{white}.}&= \dfrac{1}{\lambda}\left( \bm{P}(i\!-\!1) - \bm{k}(i) \bm{u}^T\!(i)\bm{P}(i\!-\!1) \right)\\
		\bm{\hat{w}}(i) {\color{white}.}&= \bm{\hat{w}}(i\!-\!1) + \alpha(i) \bm{k}(i)
	\end{array}
	\label{eq:RLS_Posterior}
\end{equation}
sendo, análogo ao algoritmo de filtragem de Kalman \cite{alcantara_oceans, ristic2003beyond}, separado em duas etapas: a priori \eqref{eq:RLS_Prior} e a posteriori \eqref{eq:RLS_Posterior}. Conforme a notação do algoritmo de filtragem de Kalman \cite{ristic2003beyond,alcantara_oceans}, segue que $\alpha$ é a inovação (equivalente ao $e$, somente com outra interpretação); a matriz $\bm{P}$ é a covariância dos erros de estimativa; e o $\bm{k}$, o ganho.

Vale também ressaltar a forma de inicialização convencional do algoritmo de filtragem
\begin{equation}
	\begin{array}{r@{}l}
		\bm{P}(0) {\color{white}.}&= \delta \bm{I}\\
		\bm{k}(0) {\color{white}.}&= \bm{0}\\
		\alpha(0) {\color{white}.}&= 0
	\end{array}
	\label{eq:RLS_init}
\end{equation}
sendo $\delta \geq 0$ o fator de inicialização cega \cite{alcantara_oceans}, $\bm{I}$ a matriz identidade de ordem $N$ e $\bm{0}$ o vetor coluna zero.

\subsection{Redes Adaptativas}
Em suma, a teoria de Redes Adaptativas é uma área que combina conceitos de filtragem adaptativa, teoria de grafos e otimização distribuída. Ela lida com processamento e aprendizado distribuídos em redes de agentes interconectados (nós) que podem se adaptar a ambientes dinâmicos de forma colaborativa \cite{sayed2013diffusionadaptationnetworks}, no intuito de melhorar as estimativas de determinado fenômeno de interesse.

De forma resumida, a premissa principal de redes adaptativas surge da dificuldade que pode haver em compartilhar e coordenar todos os agentes em uma central. Esta dificuldade pode ser oriunda da distância física entre os agentes e a central, a quantidade de agentes, o tempo de propagação da informação, o tempo de processamento, etc., acarretando em problemas de propagação da informação em tempo-real e prejudicando a adaptação da rede como um todo embora se tente refinar estimativas (juntando as informações estatísticas dos agentes).

Uma forma de contornar esta problemática é exatamente deixar o agente se adaptar e permitir que eles troquem informações entre si, permitindo que eles refinem suas estimativas de forma distribuída, melhorando a estimativa da rede como um todo. Do ponto de vista da problemática de filtragem Bayesiana \cite{ristic2003beyond, Kay}, as informações estatísticas trocadas entre os agentes devem coadunar para se gerar um estimador consistente e cuja estimativa convirja em probabilidade para o valor desejado \cite{ristic2003beyond,Larry, Kay}, pois, se os estimadores de cada agente separadamente for consistente acerca do fenômeno observado, realizar a média destas estimativas será um estimador do operador esperança estatística no sentido de \ac{mle}\footnote{Mais sobre \acl{mle} em \cite{Kay,Larry}.}; melhorando-se, pois, as estimativas.  

Uma das formas de os agentes trocarem informações estatísticas entre si é utilizando estratégias de difusão \cite{sayed2013diffusionadaptationnetworks}, baseadas nas conexões que cada agente possui com sua vizinhança (o grafo da vizinha) \cite{sayed2013diffusionadaptationnetworks}. Há também estratégias de consenso \cite{sayed2013diffusionadaptationnetworks}. No fundo, na forma mais comum, todas estas estratégias representam médias de estimativas ou de estatísticas associadas a optimização adaptativa (dos agentes).

Em termos de custo, considera-se uma coleção de $N_a$ agentes interessados em estimar o mesmo vetor de parâmetros $\bm{w^{o}} \in \mathbb{R}^N$, vetor coluna. Este vetor é o minimizador de alguma função de custo global \cite{sayed2013diffusionadaptationnetworks}
\begin{gather}
	\bm{w^{o}} = \arg\min\limits_{\bm{w}} J^{\text{glob}}(\bm{w})\\
	J^{\text{glob}}(\bm{w}) = \sum_{k=1}^{N_a} J_k(\bm{w})\label{eq:custo_global}
\end{gather} 
que os agentes buscam otimizar, usando técnicas como, e.g., completar quadrados em normas euclidiana \eqref{eq:funcao_custo}(ou, dependendo da forma da norma, expandindo em série de Taylor e truncando até o termo de segunda ordem \cite{sayed2013diffusionadaptationnetworks,alcantara_oceans}), {\color{blue}aproximando-se a função de custo global \eqref{eq:custo_global} por uma função custo local alternativa a qual é passível de se otimizar de forma distribuída}. Então, cada agente $k$ otimizará o seu próprio custo local utilizando, por exemplo, o método de steepest-descent\footnote{Mais sobre a técnica de steepest-descent em \cite{diniz2020adaptive}.} \cite{sayed2013diffusionadaptationnetworks}.

Considerando-se a função custo de norma euclidiana, aproxima-se \eqref{eq:custo_global} para a forma local
\begin{equation}
	\begin{array}{r@{}l}
		J_{k}^{\text{loc}}(\bm{w}) {\color{white}.}&= \sum\limits_{l\in\mathcal{N}_k}c_{lk}J_{l}(\bm{w})\\
		\sum\limits_{l\in\mathcal{N}_k} c_{lk} {\color{white}.}&= 1; \quad c_{lk} > 0, \forall l \in \mathcal{N}_k;\ c_{lk} = 0, \forall l \notin \mathcal{N}_k
	\end{array}
	\label{eq:funcao_custo_local_And_clk}
\end{equation}
obtendo-se uma função custo local estritamente convexa, supondo-se $J_{l}(\bm{w})$ convexa $ \forall l$. Então, pode-se demonstrar \cite{sayed2013diffusionadaptationnetworks}
\begin{equation}
\begin{split}
	J_{k}^{\text{loc}}(\bm{w}) &= \sum\limits_{l\in\mathcal{N}_k}c_{lk}\left( J_l(\bm{w^o}) + (\bm{w}-\bm{w^o})^T\bm{R}(\bm{w}-\bm{w^o})\right)\\
	&= \sum\limits_{l\in\mathcal{N}_k}c_{lk}J_l(\bm{w^o}) + \sum\limits_{l\in\mathcal{N}_k}c_{lk}(\bm{w}-\bm{w^o})^T\!\bm{R}(\bm{w}-\bm{w^o})\\
	&= J_k^{\text{loc}}(\bm{w^o}) + (\bm{w}-\bm{w^o})^T\!\left(\sum\limits_{l\in\mathcal{N}_k}c_{lk}\bm{R}\right)(\bm{w}-\bm{w^o})\\
	&= J_k^{\text{loc}}(\bm{w^o}) + (\bm{w}-\bm{w^o})^T\!\bm{R_k}(\bm{w}-\bm{w^o})
\end{split}
\label{eq:J_loc_expandido}
\end{equation}
sendo o instante temporal $i$ omitido por questões de simplicidade de escrita. Pode-se verificar que $\bm{R_l}$, sendo uma somatória positiva de matrizes de Riccati (matrizes positivas definidas), é uma matriz positiva definida.

Buscando-se obter a função custo global $J^{\text{glob}}$ em relação as funções custo local $J_{k}^{\text{loc}}$, tem-se, por \eqref{eq:custo_global} e \eqref{eq:funcao_custo_local_And_clk}, que
\begin{equation}
\begin{split}
	J^{\text{glob}}(\bm{w}) &= \sum\limits_{k=1}^{N_a} {\color{blue}1}\cdot J_k(\bm{w})\\
	&= \sum\limits_{k=1}^{N_a} \left({\color{blue}\sum\limits_{l=1}^{N_a}c_{kl}}\right) J_k(\bm{w})\\
	&= {\color{blue}\sum\limits_{l=1}^{N_a}}\left(\sum\limits_{k=1}^{N_a} {\color{blue}c_{kl}}J_k(\bm{w})\right)\\
	&= \sum\limits_{l=1}^{N_a} J_l^{\text{loc}}(\bm{w}) = J_k^{\text{loc}}(\bm{w}) + \sum\limits_{\forall l\neq k} J_l^{\text{loc}}(\bm{w})
\end{split}
\end{equation}
que, com base no resultado de \eqref{eq:J_loc_expandido}, pode-se verificar que
\begin{equation}
\begin{split}
	J^{\text{glob}}(\bm{w}) &= J_k^{\text{loc}}(\bm{w}) + \sum\limits_{\forall l\neq k} (\bm{w}-\bm{w^o})^T\!\bm{R_l}(\bm{w}-\bm{w^o})\\
	&\qquad\qquad\ \ \! + \sum\limits_{\forall l\neq k}J_l^{\text{loc}}(\bm{w^o})\\
	J^{\text{glob}'}(\bm{w}) &= J_k^{\text{loc}}(\bm{w}) + \sum\limits_{\forall l\neq k} (\bm{w}-\bm{w^o})^T\!\bm{R_l}(\bm{w}-\bm{w^o})
\end{split}
\label{eq:J_glob_linha}
\end{equation}
ressaltando-se que, minimizando $J^{\text{glob}'}$ em relação a $\bm{w}$ é o mesmo que minimizar $J^{\text{glob}}$ em relação a $\bm{w}$ \cite{sayed2013diffusionadaptationnetworks}, pois $J_l^{\text{loc}}(\bm{w^o})$ são termos independentes de $\bm{w}$. Reduzindo ainda mais o resultado \eqref{eq:J_glob_linha} para um determinado agente $k$, tem-se
\begin{equation}
	J_k^{\text{glob}'}(\bm{w}) = J_k^{\text{loc}}(\bm{w}) + \sum\limits_{l\in\mathcal{N}_k \backslash \lbrace k\rbrace} (\bm{w}-\bm{w^o})^T\!\bm{R_l}(\bm{w}-\bm{w^o})
\end{equation}
As funções custo $J_k^{\text{loc}}$ e $J_k^{\text{glob}'}$ são ambas associadas ao agente $k$, com a diferença de que esta última é mais próxima da função custo $J^{\text{glob}}$ que se deseja optimizar do que aquela primeira \cite{sayed2013diffusionadaptationnetworks}.

A matriz $\bm{R_l}$ pode não ser conhecida dependendo do modelo de sistema que se deseja estimar, além de outras problemáticas práticas que podem ocorrer acerca de sua estimação dela \cite{sayed2013diffusionadaptationnetworks}. Levando-se em conta o fato de $\bm{R_l}$ ser uma matriz Hermitiana e positiva definida e pelo teorema de Rayleigh-Ritz para caracterizar seus autovalores \cite{sayed2013diffusionadaptationnetworks}, põe-se como razoável a aproximação
\begin{equation}
	\bm{R_l} \approx b_{lk}\bm{I}
\end{equation}
tal que $b_{lk}\geq 0$, obtendo-se
\begin{equation}
	J_k^{\text{glob}''}(\bm{w}) = J_k^{\text{loc}}(\bm{w}) + \sum\limits_{l\in\mathcal{N}_k \backslash \lbrace k\rbrace}b_{lk} \norm{\bm{w}-\bm{w^o}}^2
	\label{eq:J_glob_linhalinha_k}
\end{equation}

Tendo-se \eqref{eq:J_glob_linhalinha_k}, pode-se utilizar o método do steepest-descent para iterar e obter as estimativas de $\bm{w}$
\begin{equation}
	\bm{\hat{w}_k}(i) = \bm{\hat{w}_k}(i\!-\!1) - \mu_k \nabla_{\bm{\hat{w}}}J_k^{\text{glob}''}(\bm{\hat{w}_k}(i-1))
	\label{eq:w_steepest_descent_glob}
\end{equation}
consequentemente
\begin{equation}
\begin{split}
	\bm{\hat{w}_k}(i) &= \bm{\hat{w}_k}(i\!-\!1) + \mu_k\sum\limits_{l\in\mathcal{N}_k}c_{lk} \nabla_{\bm{\hat{w}}}J_l(\bm{\hat{w}_k}(i-1))\\
	& \qquad\qquad %
	+ \mu_k\!\sum\limits_{l\in\mathcal{N}_k \backslash \lbrace k\rbrace}\! b_{lk}\left(\bm{\hat{w}_k}(i-1) - \bm{w^o}\right)
	\label{eq:}
\end{split}
\end{equation}
levando-se em consideração as premissas de construção da rede conforme \cite{sayed2013diffusionadaptationnetworks} e assumindo que as conexões da rede são simétricas, tem-se que, por construção
\begin{gather}
	\left\lbrace\begin{matrix*}[l]
		a_{kk} &\!\!\!\! := 1-\mu_k\!\sum\limits_{l\in\mathcal{N}_k \backslash \lbrace k\rbrace}\! b_{lk}\\
		a_{lk} &\!\!\!\! := \mu_kb_{lk};\ l\in\mathcal{N}_k\backslash\lbrace k\rbrace\\
		a_{lk} &\!\!\!\! := 0;\ \qquad\!\! l\notin\mathcal{N}_k\\
		\end{matrix*}\right.\label{eq:premissa_a1}\\
	    \bm{C} = \bm{A}^T \ \Rightarrow\ c_{lk} = a_{kl}\label{eq:premissa_a2}
\end{gather}

Com as premissas que corroboram para os resultados \eqref{eq:premissa_a1} e \eqref{eq:premissa_a2}, tem-se que a matriz $\bm{A}$ determina a intensidade das conexões entre os agentes. Ela será responsável pela forma como se dão as estratégias de difusão e de consenso.

\subsubsection{Estratégia Difusão Adapt Then Combine}
A estratégia de difusão \ac{atc} trocando apenas as estimativas \cite{sayed2013diffusionadaptationnetworks}, primeiro adapta o resultado da estimativa, depois pondera as estimativas entre os agentes de determinado vizinhança $\mathcal{N}$. Seja, e.g., o agente $k$, a vizinhança deste agente é determinada por $\mathcal{N}_k$. Então, o \ac{atc} das estimativas fica
\begin{equation}
	\begin{array}{r@{}l}
		\bm{\hat{\psi}_k}(i) {\color{white}.}&= \bm{\hat{w}_k}(i\!-\!1) + \mu_k \nabla_{\bm{\hat{w}}}J_k\left(\bm{\hat{w}_k}(i\!-\!1)\right)\\
		\bm{\hat{w}_k}(i) {\color{white}.}&= \sum\limits_{l\in\mathcal{N}_k} a_{lk}\bm{\hat{\psi}_l}(i)\\
		\sum\limits_{l\in\mathcal{N}_k} a_{lk} {\color{white}.}&= 1; \quad a_{lk} \geq 0,\ \forall l \in \mathcal{N}_k 
	\end{array}
	\label{eq:ATC_without_info}
\end{equation}
sendo $\bm{\hat{\psi}}$ um passo de adaptação local intermediário; $\mu_k \nabla_{\bm{\hat{w}}}J_k\left(\bm{\hat{w}_k}(i\!-\!1)\right)$ a representação teórica da técnica de adaptação utilizada pelo agente $k$ individualmente; e $a_{lk}$ a intensidade da conexão do agente $l$ ao agente $k$ (e.g., $a_{lk} = 0$ indica que não há conexão do agente $l$ para o agente $k$).

No caso em que a difusão \ac{atc} também troca estatísticas associadas a optimização adaptativa \cite{sayed2013diffusionadaptationnetworks}, tem-se que
\begin{equation}
	\begin{array}{r@{}l}
		\bm{\hat{\psi}_k}(i) {\color{white}.}&= \bm{\hat{w}_k}(i\!-\!1) + \mu_k \sum\limits_{l\in\mathcal{N}_k} c_{lk} \nabla_{\bm{\hat{w}}}J_l\left(\bm{\hat{w}_k}(i\!-\!1)\right)\\
		\bm{\hat{w}_k}(i) {\color{white}.}&= \sum\limits_{l\in\mathcal{N}_k} a_{lk}\bm{\hat{\psi}_l}(i)\\
		\sum\limits_{l\in\mathcal{N}_k} a_{lk} {\color{white}.}&= 1; \quad a_{lk} > 0, \forall l \in \mathcal{N}_k;\ a_{lk} = 0, \forall l \notin \mathcal{N}_k
	\end{array}
	\label{eq:ATC_info}
\end{equation}

\subsubsection{Estratégia Difusão Combine Then Adapt}
A estratégia de difusão \ac{cta} trocando apenas as estimativas \cite{sayed2013diffusionadaptationnetworks}, primeiro pondera as estimativas entre os agentes, depois adapta o resultado da estimativa à luz da nova observação. Seja, e.g., o agente $k$, a vizinhança deste agente é determinada por $\mathcal{N}_k$. Então, o \ac{cta} das estimativas fica
\begin{equation}
	\begin{array}{c@{}l}
		\bm{\hat{\psi}_k}(i\!-\!1) {\color{white}.}&= \sum\limits_{l\in\mathcal{N}_k} a_{lk}\bm{\hat{w}_l}(i\!-\!1)\\
		\bm{\hat{w}_k}(i) {\color{white}.}&= \bm{\hat{\psi}_k}(i\!-\!1) + \mu_k \nabla_{\bm{\hat{\psi}}}J_k\left(\bm{\hat{\psi}_k}(i\!-\!1)\right)\\
		\sum\limits_{l\in\mathcal{N}_k} a_{lk} {\color{white}.}&= 1; \quad a_{lk} \geq 0,\ \forall l \in \mathcal{N}_k 
	\end{array}
	\label{eq:CTA_without_info}
\end{equation}

No caso em que em que a difusão \ac{cta} também troca estatísticas associadas a optimização adaptativa \cite{sayed2013diffusionadaptationnetworks}, tem-se que
\begin{equation}
	\begin{array}{c@{}l}
		\bm{\hat{\psi}_k}(i\!-\!1) {\color{white}.}&= \sum\limits_{l\in\mathcal{N}_k} a_{lk}\bm{\hat{w}_l}(i\!-\!1)\\
		\bm{\hat{w}_k}(i) {\color{white}.}&= \bm{\hat{\psi}_k}(i\!-\!1) + \mu_k\! \sum\limits_{l\in\mathcal{N}_k}\!c_{lk}\nabla_{\bm{\hat{\psi}}}J_l\left(\bm{\hat{\psi}_l}(i\!-\!1)\right)\\
		\sum\limits_{l\in\mathcal{N}_k} a_{lk} {\color{white}.}&= 1; \quad a_{lk} > 0, \forall l \in \mathcal{N}_k;\ a_{lk} = 0, \forall l \notin \mathcal{N}_k
	\end{array}
	\label{eq:CTA_info}
\end{equation}

\section{Metodologia}\label{sec:metodologia}
O objetivo do trabalho é verificar os conceitos teóricos de redes adaptativas, utilizando-se agentes com as mesmas técnicas de estimação (individual) para se determinar o estado $\bm{w^o}$ de um determinado fenômeno observado, almejando-se utilizar tais conceitos em aplicações futuras envolvendo o trabalho \cite{alcantara_cognitive_sonar} e rede de sensores. Para isto, comparam-se duas redes: uma não-cooperativa e uma cooperativa \ac{atc} de estimativa (no intuito de se testar a difusão de estimativas). 

Implementam-se também 3 técnicas de filtragem para os agentes. Foram utilizadas as técnicas de filtragem de Wiener, \ac{lms} e \ac{rls}. Com isto, serão feitas 3 comparações de redes: uma cujo todos os agentes possuem filtragem de Wiener, uma cujo todos os agentes possuem filtragem LMS, e uma cujo todos os agentes possuem filtragem RLS. Logo, pode-se comparar, também, a influência da técnica de filtragem na rede e no fenômeno simulado.

% Comparam-se os resultados para os métodos de rede adaptativa \ac{atc} com a de rede não-cooperativa dos 3 tipos de filtragem e, também, entre os tipos de filtragem.

Faz-se o método de Monte-Carlo como técnica de reamostragem estatística \cite{Larry}, com $M=300$ realizações. 

\subsection{Dados experimentais}
Os dados experimentais empregados na avaliação do algoritmo foram simulados em MATLAB 2023b. Foi simulada uma uma dinâmica de estado, conforme \refF{fig:diagrama_de_blocos_simulacao}, utilizando-se um processo $u$ \ac{iid} com potência de ruído $\sigma^2_{u}=0.25$ e média $\mu_{u} = 1$, e um processo ruidoso $\nu$ \ac{iid} que corrompe a observação $d$ com potência de ruído  $\sigma^2_{\nu}=0.04$ e média nula. O estado almejado encontra-se como sendo $\bm{w^o} = [.1\ .3\ .7]^T$, ou seja, a ordem do nosso sistema regressivo é 3 e este híper-parâmetro já será dado como certo para todas as técnicas de filtragem simuladas.

O número de agentes utilizado é na topologia simulada é $N_a = 6$, o número de amostras temporais é $N=1000$. Todos os filtros são inicializados de forma padrão (inicialização cega \cite{alcantara_oceans}).

O filtro de Wiener é configurado com $\mu=0.1$, $\epsilon=0$, e tamanho de janela de amostras (\emph{buffer}) igual a 10, tanto para entrada $\bm{u}$, quanto $d$. Este \emph{buffer} é utilizado para carregar as amostras no estimador de esperança estatística que realiza as estimativas de $\bm{\hat{R}}$ e $\bm{\hat{r}}$.

O filtro \ac{lms} é configurado com $\mu=0.3$, e $\epsilon=1e-5$.

O filtro \ac{rls} é configurado com $\lambda=0.85$, e $\delta=1e-3$.

A rede de agentes possui a topologia dada pela seguinte matriz $A$ em \eqref{eq:matrix_A}. Em termos ilustrativos, monstram-se as conexões entre os agentes no grafo representado na \refF{fig:graph_sim}.
\begin{equation}
	A^T = \begin{bmatrix}
		1/3 & 1/3 & 1/3 &  0  &  0  &  0 \\
		1/2 & 1/2 &  0  &  0  &  0  &  0 \\
		1/3 &  0  & 1/3 & 1/3 &  0  &  0 \\
		0   &  0  & 1/4 & 1/4 & 1/4 & 1/4 \\
		0   &  0  &  0  & 1/2 & 1/2 &  0 \\
		0   &  0  &  0  & 1/2 &  0  & 1/2
	\end{bmatrix}
	\label{eq:matrix_A}
\end{equation}

\begin{figure}[H]
    \centering
    \includegraphics[width=.7\linewidth]{./images/graph.png}
    \caption{Grafo ilustrando as conexões entre os agentes $k=1,2,...,6$, com base na matriz $\bm{A}$ simulada.}
    \label{fig:graph_sim}
\end{figure}


\subsection{Recursos Computacionais}
A rede ART foi implementada em MATLAB 2023b, utilizando-se um computador com 32GB de RAM, 500 GB de SSD, com processador Intel i5 de oitava geração. O código se encontra no repositório git \url{https://github.com/caiocmpalcantara/CPE769_multiagent_adaptive_networks}.

\section{Resultados}\label{sec:resultados}
Os gráficos fornecidos nas figuras desta Seccção mostram o desempenho das redes adaptativas no referido cenário simu-lado com base na \refF{fig:diagrama_de_blocos_simulacao} e cujas variáveis foram descritas na Secção \ref{sec:metodologia}.

Utilizam-se o \ac{mse} e o \ac{msd} como figura de mérito na avaliação de desempenho das referidas redes propostas. Além disso, as mesmas realizações são utilizadas para todas as redes, no intuito de se manter um critério mais fidedigno de comparação.

\subsection{Filtragem de Wiener}
Nas figuras \ref{fig:result_wiener_noncoop_realization_0} e \ref{fig:result_wiener_coop_realization_0} pode se visualizar uma realização, monstrando-se a estimativa $\hat{y}$ dos agentes $k$ = 1, 2 e 3, para as redes não-cooperativa e cooperativa \ac{atc} estimativas, respectivamente. Também se mostra a observação $d$. 
\begin{figure}[H]
    \centering
    \includegraphics[width=1.\linewidth]{./images/realization_M300_N1000_Na6_Wiener_nwin10_noncoop.png}
    \caption{Realização $m=1$, amostras temporais de $\hat{y}$ e de $d$ para a rede não-cooperativa.}
    \label{fig:result_wiener_noncoop_realization_0}
\end{figure}
\begin{figure}[H]
    \centering
    \includegraphics[width=1.\linewidth]{./images/realization_M300_N1000_Na6_Wiener_nwin10_coop.png}
    \caption{Realização $m=1$, amostras temporais de $\hat{y}$ e de $d$ para a rede cooperativa ATC estimativas.}
    \label{fig:result_wiener_coop_realization_0}
\end{figure}

As figuras \ref{fig:result_wiener_noncoop_realization_1} e \ref{fig:result_wiener_coop_realization_1} fornecem uma ampliação na escala de tempo, para que se possa melhor visualizar a região de transição inicial.
\begin{figure}[H]
    \centering
    \includegraphics[width=1.\linewidth]{./images/realization_M300_N1000_Na6_Wiener_nwin10_noncoop2.png}
    \caption{Realização $m=1$, amostras temporais de $\hat{y}$ e de $d$ para a rede não-cooperativa. Amplificação na linha do tempo.}
    \label{fig:result_wiener_noncoop_realization_1}
\end{figure}
\begin{figure}[H]
    \centering
    \includegraphics[width=1.\linewidth]{./images/realization_M300_N1000_Na6_Wiener_nwin10_coop2.png}
    \caption{Realização $m=1$, amostras temporais de $\hat{y}$ e de $d$ para a rede cooperativa ATC estimativas. Amplificação na linha do tempo.}
    \label{fig:result_wiener_coop_realization_1}
\end{figure}

As figuras \ref{fig:result_wiener_mse_agent4} e \ref{fig:result_wiener_msd_agent4} mostram a comparação entre as redes não-cooperativas e cooperativas \ac{atc} estimativas para o agente $k=4$, em termos de \ac{mse} e \ac{msd} respectivamente.
\begin{figure}[H]
    \centering
    \includegraphics[width=1.\linewidth]{./images/MSE_M300_N1000_Wiener_agent4.png}
    \caption{Comparação de MSE entre redes não-cooperativa e cooperativa ATC estimativas. Agente $k=4$.}
    \label{fig:result_wiener_mse_agent4}
\end{figure}
\begin{figure}[H]
    \centering
    \includegraphics[width=1.\linewidth]{./images/MSD_M300_N1000_Wiener_agent4.png}
    \caption{Comparação de MSD entre redes não-cooperativa e cooperativa ATC estimativas. Agente $k=4$.}
    \label{fig:result_wiener_msd_agent4}
\end{figure}

Para o agente $k=2$, tem-se as figuras \ref{fig:result_wiener_mse_agent2} e \ref{fig:result_wiener_msd_agent2} em termos de \ac{mse} e \ac{msd} respectivamente.
\begin{figure}[H]
    \centering
    \includegraphics[width=1.\linewidth]{./images/MSE_M300_N1000_Wiener_agent2.png}
    \caption{Comparação de MSE entre redes não-cooperativa e cooperativa ATC estimativas. Agente $k=2$.}
    \label{fig:result_wiener_mse_agent2}
\end{figure}
\begin{figure}[H]
    \centering
    \includegraphics[width=1.\linewidth]{./images/MSD_M300_N1000_Wiener_agent2.png}
    \caption{Comparação de MSD entre redes não-cooperativa e cooperativa ATC estimativas. Agente $k=2$.}
    \label{fig:result_wiener_msd_agent2}
\end{figure}

\subsection{Filtragem LMS}
Nas figuras \ref{fig:result_lms_noncoop_realization_0} e \ref{fig:result_lms_coop_realization_0} pode se visualizar uma realização, monstrando-se a estimativa $\hat{y}$ dos agentes $k$ = 1, 2 e 3, para as redes não-cooperativa e cooperativa \ac{atc} estimativas, respectivamente. Também se mostra a observação $d$. 
\begin{figure}[H]
    \centering
    \includegraphics[width=1.\linewidth]{./images/realization_M300_N1000_Na6_LMS_noncoop.png}
    \caption{Realização $m=1$, amostras temporais de $\hat{y}$ e de $d$ para a rede não-cooperativa.}
    \label{fig:result_lms_noncoop_realization_0}
\end{figure}
\begin{figure}[H]
    \centering
    \includegraphics[width=1.\linewidth]{./images/realization_M300_N1000_Na6_LMS_coop.png}
    \caption{Realização $m=1$, amostras temporais de $\hat{y}$ e de $d$ para a rede cooperativa ATC estimativas.}
    \label{fig:result_lms_coop_realization_0}
\end{figure}

As figuras \ref{fig:result_lms_noncoop_realization_1} e \ref{fig:result_lms_coop_realization_1} fornecem uma ampliação na escala de tempo, para que se possa melhor visualizar a região de transição inicial.
\begin{figure}[H]
    \centering
    \includegraphics[width=1.\linewidth]{./images/realization_M300_N1000_Na6_LMS_noncoop2.png}
    \caption{Realização $m=1$, amostras temporais de $\hat{y}$ e de $d$ para a rede não-cooperativa. Amplificação na linha do tempo.}
    \label{fig:result_lms_noncoop_realization_1}
\end{figure}
\begin{figure}[H]
    \centering
    \includegraphics[width=1.\linewidth]{./images/realization_M300_N1000_Na6_LMS_coop2.png}
    \caption{Realização $m=1$, amostras temporais de $\hat{y}$ e de $d$ para a rede cooperativa ATC estimativas. Amplificação na linha do tempo.}
    \label{fig:result_lms_coop_realization_1}
\end{figure}

As figuras \ref{fig:result_lms_mse_agent4} e \ref{fig:result_lms_msd_agent4} mostram a comparação entre as redes não-cooperativas e cooperativas \ac{atc} estimativas para o agente $k=4$, em termos de \ac{mse} e \ac{msd} respectivamente.
\begin{figure}[H]
    \centering
    \includegraphics[width=1.\linewidth]{./images/MSE_M300_N1000_LMS_agent4}
    \caption{Comparação de MSE entre redes não-cooperativa e cooperativa ATC estimativas. Agente $k=4$.}
    \label{fig:result_lms_mse_agent4}
\end{figure}
\begin{figure}[H]
    \centering
    \includegraphics[width=1.\linewidth]{./images/MSD_M300_N1000_LMS_agent4}
    \caption{Comparação de MSD entre redes não-cooperativa e cooperativa ATC estimativas. Agente $k=4$.}
    \label{fig:result_lms_msd_agent4}
\end{figure}

Para o agente $k=2$, tem-se as figuras \ref{fig:result_lms_mse_agent2} e \ref{fig:result_lms_msd_agent2} em termos de \ac{mse} e \ac{msd} respectivamente.
\begin{figure}[H]
    \centering
    \includegraphics[width=1.\linewidth]{./images/MSE_M300_N1000_LMS_agent2}
    \caption{Comparação de MSE entre redes não-cooperativa e cooperativa ATC estimativas. Agente $k=2$.}
    \label{fig:result_lms_mse_agent2}
\end{figure}
\begin{figure}[H]
    \centering
    \includegraphics[width=1.\linewidth]{./images/MSD_M300_N1000_LMS_agent2}
    \caption{Comparação de MSD entre redes não-cooperativa e cooperativa ATC estimativas. Agente $k=2$.}
    \label{fig:result_lms_msd_agent2}
\end{figure}

\subsection{Filtragem RLS}
Nas figuras \ref{fig:result_rls_noncoop_realization_0} e \ref{fig:result_rls_coop_realization_0} pode se visualizar uma realização, monstrando-se a estimativa $\hat{y}$ dos agentes $k$ = 1, 2 e 3, para as redes não-cooperativa e cooperativa \ac{atc} estimativas, respectivamente. Também se mostra a observação $d$. 
\begin{figure}[H]
    \centering
    \includegraphics[width=1.\linewidth]{./images/realization_M300_N1000_Na6_RLS_noncoop.png}
    \caption{Realização $m=1$, amostras temporais de $\hat{y}$ e de $d$ para a rede não-cooperativa.}
    \label{fig:result_rls_noncoop_realization_0}
\end{figure}
\begin{figure}[H]
    \centering
    \includegraphics[width=1.\linewidth]{./images/realization_M300_N1000_Na6_RLS_coop.png}
    \caption{Realização $m=1$, amostras temporais de $\hat{y}$ e de $d$ para a rede cooperativa ATC estimativas.}
    \label{fig:result_rls_coop_realization_0}
\end{figure}

As figuras \ref{fig:result_rls_noncoop_realization_1} e \ref{fig:result_rls_coop_realization_1} fornecem uma ampliação na escala de tempo, para que se possa melhor visualizar a região de transição inicial.
\begin{figure}[H]
    \centering
    \includegraphics[width=1.\linewidth]{./images/realization_M300_N1000_Na6_RLS_noncoop2.png}
    \caption{Realização $m=1$, amostras temporais de $\hat{y}$ e de $d$ para a rede não-cooperativa. Amplificação na linha do tempo.}
    \label{fig:result_rls_noncoop_realization_1}
\end{figure}
\begin{figure}[H]
    \centering
    \includegraphics[width=1.\linewidth]{./images/realization_M300_N1000_Na6_RLS_coop2.png}
    \caption{Realização $m=1$, amostras temporais de $\hat{y}$ e de $d$ para a rede cooperativa ATC estimativas. Amplificação na linha do tempo.}
    \label{fig:result_rls_coop_realization_1}
\end{figure}

As figuras \ref{fig:result_rls_mse_agent4} e \ref{fig:result_rls_msd_agent4} mostram a comparação entre as redes não-cooperativas e cooperativas \ac{atc} estimativas para o agente $k=4$, em termos de \ac{mse} e \ac{msd} respectivamente.
\begin{figure}[H]
    \centering
    \includegraphics[width=1.\linewidth]{./images/MSE_M300_N1000_RLS_agent4}
    \caption{Comparação de MSE entre redes não-cooperativa e cooperativa ATC estimativas. Agente $k=4$.}
    \label{fig:result_rls_mse_agent4}
\end{figure}
\begin{figure}[H]
    \centering
    \includegraphics[width=1.\linewidth]{./images/MSD_M300_N1000_RLS_agent4}
    \caption{Comparação de MSD entre redes não-cooperativa e cooperativa ATC estimativas. Agente $k=4$.}
    \label{fig:result_rls_msd_agent4}
\end{figure}

Para o agente $k=2$, tem-se as figuras \ref{fig:result_rls_mse_agent2} e \ref{fig:result_rls_msd_agent2} em termos de \ac{mse} e \ac{msd} respectivamente.
\begin{figure}[H]
    \centering
    \includegraphics[width=1.\linewidth]{./images/MSE_M300_N1000_RLS_agent2}
    \caption{Comparação de MSE entre redes não-cooperativa e cooperativa ATC estimativas. Agente $k=2$.}
    \label{fig:result_rls_mse_agent2}
\end{figure}
\begin{figure}[H]
    \centering
    \includegraphics[width=1.\linewidth]{./images/MSD_M300_N1000_RLS_agent2}
    \caption{Comparação de MSD entre redes não-cooperativa e cooperativa ATC estimativas. Agente $k=2$.}
    \label{fig:result_rls_msd_agent2}
\end{figure}

% A tabela abaixo mostra o resultado (para o cenário 2) de uma das realizações cujo número de neurônios é igual a 5
% \begin{table}[H]
%     % \fontsize{7pt}{7pt}\selectfont
%     \centering
%     \begin{tabular}{c|ccccc}
%     \toprule
%     Features & $N_1$ & $N_2$ & $N_3$ & $N_4$ & $N_5$\\
%     \hline\\[-.5em]
%     AoA [º]         & 149.8   &  125.1   &  114.3   &  136.1   &  146.4  \\[.1em]
%     $\mathcal{C}_i$ & 3       &  3       &  6       &  3       &  5      \\[.1em]
%     $r$ [m]         & 7030.7  &  6603.5  &  5985.4  &  6832.6  &  7004.4 \\[.1em]
%     \toprule
%     \end{tabular}
%     \caption{Cenário simulado 2: centros das sinápses $\bm{w_i}$ dos respectivos neurônios $N_i$ para uma determinada realização.}
%     \label{tab:cenario02_wi}
% \end{table}
        
\section{Conclusões}\label{sec:conclusoes}
Pelos resultados pode-se constatar que a rede cooperativa gera estimativas de $\bm{\hat{w}}$ melhores do que as redes não-cooperativas em todos os tipos de agentes utilizados nas simulações, conforme é de se esperar em termos de teoria da estimação e de convergência de variáveis aleatórias \cite{Larry,Kay}. Isto foi alcançado dadas as premissas da simulação de $u$ e de $d$ e pela dinâmica de sistema realizada em conformidade com a \refF{fig:diagrama_de_blocos_simulacao}.

Outro fato interessante de ser notado é que a medida de \ac{mse} das redes não-cooperativas obtiveram um valor de \ac{mse} ligeiramente menor do que as redes cooperativas \ac{atc} estimativa, conforme se pode verificar nas figuras \ref{fig:result_lms_mse_agent4}, \ref{fig:result_lms_mse_agent2}, \ref{fig:result_rls_mse_agent4} e \ref{fig:result_rls_mse_agent2}, obtidas por agentes com técnicas de filtragem LMS e RLS. 

Embora estes valores de \ac{mse} sejam menores para as não-cooperativas, isto não indica que elas estejam desempenhando o objetivo\footnote{No contexto simulado neste trabalho, a estimação do estado $\bm{w^o}$.} melhor do que as cooperativas, pois os valores de \ac{msd} das redes cooperativas são significativamente menores\footnote{Os valores de \acs{msd} das redes cooperativas são acerca de 6 dB menores do os das redes não-cooperativas. Levando em consideração o agente mais isolado que é o $k=2$. Para o agente com a maior quantidade de agentes na vizinhança, $k=4$, os resultados chegam a ser ainda menores.} do que os valores de \ac{msd} das redes não-cooperativas; verifica-se, pois, que o \ac{mse} é uma medida complementar e não avalia se o objetivo foi de fato atingido.

Em relação as técnicas de filtragem utilizada pelos agentes em rede na dinâmica simulada, pode-se perceber que os agentes com a técnica de filtragem de Wiener obtiveram o menor transiente, seguido por aqueles de \acs{rls} e, depois, de \acs{lms}, conforme figuras \ref{fig:result_wiener_msd_agent4}, \ref{fig:result_wiener_msd_agent2}, \ref{fig:result_lms_msd_agent4}, \ref{fig:result_lms_msd_agent2}, \ref{fig:result_rls_msd_agent4} e \ref{fig:result_rls_msd_agent2}. Isto se dá predominantemente pela característica das técnicas de filtragem \cite{diniz2020adaptive}

Pelos resultados expostos nas tabelas \ref{tab:resultados_a4} e \ref{tab:resultados_a2}, pode-se cons-tatar a melhoria, em termos \ac{msd}, que as redes cooperativas adicionaram, comprovando que a obtenção de informação dos agentes de determinada vizinhança ajudam na melhoria das estimativas. 

\begin{table}[H]
    \fontsize{10pt}{9pt}\selectfont
    \centering
    \begin{tabular}{c|ccc}
    \toprule
	& \multicolumn{3}{c}{Filtragens}\\[.2em]
    Redes & Wiener & LMS & RLS\\[.5em]
    \cline{2-4}\\[-.5em]
    Não-coop         	& -21.73$\pm$0.38  &  -21.65$\pm$1.07  &  -16.87$\pm$1.42 \\[.1em]
    ATC $\bm{\hat{w}}$  & -28.66$\pm$0.33  &  -28.65$\pm$1.17  &  -23.39$\pm$1.47 \\[.1em]
    \toprule
    \end{tabular}
    \caption{Resultados obtidos de MSD para o agente $k=4$, passado o transiente das filtragens (a partir de $i=150$). As colunas representam as técnicas de filtragem utilizadas pelos agentes da rede. As linhas representam as técnicas de adaptação das redes.}
    \label{tab:resultados_a4}
\end{table}

\begin{table}[H]
    \fontsize{10pt}{9pt}\selectfont
    \centering
    \begin{tabular}{c|ccc}
    \toprule
	& \multicolumn{3}{c}{Filtragens}\\[.2em]
    Redes & Wiener & LMS & RLS\\[.5em]
    \cline{2-4}\\[-.5em]
    Não-coop         	& -21.71$\pm$0.36  &  -21.67$\pm$1.08  &  -16.86$\pm$1.37 \\[.1em]
    ATC $\bm{\hat{w}}$  & -27.54$\pm$0.27  &  -27.40$\pm$1.33  &  -21.77$\pm$1.55 \\[.1em]
    \toprule
    \end{tabular}
    \caption{Resultados obtidos de MSD para o agente $k=2$, passado o transiente das filtragens (a partir de $i=150$). As colunas representam as técnicas de filtragem utilizadas pelos agentes da rede. As linhas representam as técnicas de adaptação das redes.}
    \label{tab:resultados_a2}
\end{table}

Ao se comparar os resultados obtidos nas tabelas \ref{tab:resultados_a4} e \ref{tab:resultados_a2}, representativos dos agentes $k=4$ e $k=2$, respectivamente, pode-se perceber que, se um agente estiver mais isolado dentro da rede (\refF{fig:graph_sim}), como é o caso do agente $k=2$, as estimativas dele não conseguem se refinar tanto quanto as dos agentes que possuem mais conexões ou estão mais próximos de agentes com muitas conexões. Esta ideia implica uma certa distribuição e propagação da informação útil ao longo da rede.

Vale também observar que as redes com o algoritmo de filtragem \ac{rls} não desempenhou o objetivo tão bem quanto as redes com os algoritmos de filtragem de Wiener e LMS. Provavelmente isto se deu pelo fato de não se ter feito uma optimização em relação a híperparâmetros do algoritmo de filtragem \ac{rls} para este tipo de dinâmica simulada e implementada conforme a \refF{fig:diagrama_de_blocos_simulacao}. Ou pode ter havido um detalhe de implementação que possa ter passado desapercebido.

Resume-se que:
\begin{itemize}
    \item Redes adaptativas
    \begin{itemize}
        \item Melhoria de desempenho em relação a rede não-cooperativa.
        \item Verificou-se a capacidade de sistemas distribuídos.
        \item Agentes fracamente conectados (ou isolados) podem não conseguir melhorar suas estimativas tanto quanto os agentes com muitas conexões (do ponto de vista de grafos).
    \end{itemize}
    \item Técnicas de estimação
    \begin{itemize}
        \item Podem ser melhoradas se aplicadas com fusão de informação de outros estimadores na vizinhança
    \end{itemize}
\end{itemize}

\section{Trabalhos Futuros}\label{sec:fugure-works}
Este trabalho revelou o potencial da teoria de Redes Adaptativas e Sistemas Multiagente, no intuito de se trabalhar com rede de sensores que colaboram para obter estimativas mais refinadas. Almeja-se implementar este tipo de teoria em sistemas de SONAR Ativo Cognitivo \cite{alcantara_cognitive_sonar}, no intuito de fazer estimativas mais refinadas do estado dinâmico de alvos dentro de um cenário tático \cite{owen2007anti}, auxiliando sistemas de armas entre outros \cite{alcantara_oceans}.

Visa-se continuar estas simulações, refinando a biblioteca no repositório git \url{https://github.com/caiocmpalcantara/CPE769_multiagent_adaptive_networks}, e implementando-a de forma orientada a objetos para mais rápido desenvolverem sistemas táticos reais. A continuação será implementando agentes com técnicas de filtragem de Kalman; a implementação da troca de informações estatísticas entre os agentes (\ac{atc} e \ac{cta} de estimativas $\bm{\hat{w}}$ e de informações estatísticas \cite{sayed2013diffusionadaptationnetworks}); implementação de simulação de estados da dinâmica de navios e de submarinos \cite{alcantara_oceans}; e implementação de atrasos de propagação da informação.

No futuro também se deseja aplicar as técnicas \emph{multitask} para a troca de informações estatística diversas entre os agentes, e com agentes variando o tipo de técnica de filtragem dentro de uma mesma rede adaptativa.

Também se deseja verificar a parte matemática, demonstrando-se a capacidade de convergência para estudos de caso, utilizando-se técnicas de análise real \cite{neri2021analise,elon2020analise} para fazer truncagens e manipulações, técnicas matriciais com base nos teoremas existentes, etc.


\bibliographystyle{IEEEtran}
\bibliography{main}

% \begin{thebibliography}{00}
% \bibitem{iccsp.2011.5739284} V. Babu, M. Shankar, Majumdar, Sourav and Sanagapallea Koteswara Rao, 2011. IMM - Unscented Kalman Filter based tracking of maneuvering targets using active sonar measurements. 10.1109/ICCSP.2011.5739284.
% %
% \bibitem{Singer1970} R. A. Singer, ``Estimating Optimal Tracking Filter Performance for Manned Maneuvering Targets," in IEEE Transactions on Aerospace and Electronic Systems, vol. AES-6, no. 4, pp. 473-483, July 1970, doi: 10.1109/TAES.1970.310128.% Viés mais estocástico, fala sobre VAR ser taxa de manobrabilidade
% %
% % \bibitem{iet-rsn.2014.0142} Jin, B., Jiu, B., Su, T., Liu, H. and Liu, G. (2015), Switched Kalman filter-interacting multiple model algorithm based on optimal autoregressive model for manoeuvring target tracking. IET Radar Sonar Navig., 9: 199-209. https://doi.org/10.1049/iet-rsn.2014.0142.% Viés mais estocástico, fala sobre VAR ser taxa de manobrabilidade
% %
% \bibitem{ICAC3N53548.2021.9725728} K. S. Divya, S. K. Rao, K. S. Ramesh and G. N. Divya, "Application of Particle Filter for Passive Underwater Bearing only Tracking," 2021 3rd International Conference on Advances in Computing, Communication Control and Networking (ICAC3N), 2021, pp. 1822-1827, doi: 10.1109/ICAC3N53548.2021.9725728.
% % \bibitem{cmc.2010.239} M. Wan, P. Li and T. Li, "Tracking Maneuvering Target with Angle-Only Measurements Using IMM Algorithm Based on CKF," 2010 International Conference on Communications and Mobile Computing, 2010, pp. 92-96, doi: 10.1109/CMC.2010.239.
% %
% % \bibitem{Benedict_a-b} T. Benedict and G. Bordner, ``Synthesis of an optimal set of radar track-while-scan smoothing equations," in IRE Transactions on Automatic Control, vol. 7, no. 4, pp. 27-32, July 1962, doi: 10.1109/TAC.1962.1105477. % Filtro alpha-beta
% %
% %S. Coraluppi, C. Carthel and R. Prengaman, "Wide-Area Multistatic Sonar Tracking," 2021 IEEE 24th International Conference on Information Fusion (FUSION), 2021, pp. 1-8, doi: 10.23919/FUSION49465.2021.9626888.
% \bibitem{iet-rsn.2013.0095} Chang, G. (2014), Marginal unscented Kalman filter for cross-correlated process and observation noise at the same epoch. IET Radar Sonar Navig., 8: 54-64. https://doi.org/10.1049/iet-rsn.2013.0095.% Viés mais estocástico
% %
% \bibitem{iet-rsn.2013.0408} Shen, Haijun; Karlgaard, Christopher D.: 'Sensitivity reduction of unscented Kalman filter about parameter uncertainties', IET Radar, Sonar \&{}amp; Navigation, 2015, 9, (4), p. 374-383, DOI: 10.1049/iet-rsn.2013.0408.% Viés mais estocástico
% %
% \bibitem{FUSION49465.2021.9626888} S. Coraluppi, C. Carthel and R. Prengaman, "Wide-Area Multistatic Sonar Tracking," 2021 IEEE 24th International Conference on Information Fusion (FUSION), 2021, pp. 1-8, doi: 10.23919/FUSION49465.2021.9626888.
% % \bibitem{icnnsp.2003.1279404} Y. Huang, J. Xu and C. Zhang, "Synthetic aperture sonar movement estimation - the adaptive Kalman filter approach," International Conference on Neural Networks and Signal Processing, 2003. Proceedings of the 2003, 2003, pp. 830-833 Vol.1, doi: 10.1109/ICNNSP.2003.1279404.% Segue um algoritmo de filtragem de Kalman bem objetivo
% %
% \bibitem{TAES.1979.308793}V. J. Aidala, "Kalman Filter Behavior in Bearings-Only Tracking Applications," in IEEE Transactions on Aerospace and Electronic Systems, vol. AES-15, no. 1, pp. 29-39, Jan. 1979, doi: 10.1109/TAES.1979.308793.
% %
% \bibitem{Kay} S. M. Kay, Fundamentals of Statistical Signal Processing: Estimation Theory. New Jersey: Prentice-Hall, 1993, pp.133--155; 379--476.
% %
% \bibitem{Grewal} Mohinder S. Grewal, Angus P. Andrews, Kalman Filtering: Theory and Practice with MATLAB, 4th Edition, John Wiley \& Sons, 2015, pp.1--68;223--231.
% %
% % \bibitem{plans.2006.1650706} S. K. Rao, ``Pseudo Linear Kalman Filter For Underwater Target Location Using Intercept Sonar Measurements," 2006 IEEE/ION Position, Location, And Navigation Symposium, 2006, pp. 1036-1039, doi: 10.1109/PLANS.2006.1650706.% SONAR PASSIVO - Aproveitar o Introduction
% %
% \bibitem{Haykin} Simon Haykin, Kalman Filtering and Neural Networks, John Wiley \& Sons, 2001, pp.1--64
% %
% \bibitem{Baccala} Luiz A. Baccala, Introdução a Processos Estocásticos, EPUSP, 2017, pp.49--190.
% %
% \bibitem{LeonGarcia} Alberto Leon-Garcia, Probability, Statistics and Random Processes for Electrical Engineering, 3rd Edition, Pearson Prentice Hall, 2008, pp.488--633.
% %
% \bibitem{Oppenheim} Alan V. Oppenheim, Ronald W. Schafer, Discrete-Time Signal Processing, 3rd Edition, Pearson Prentice Hall, 2009, pp.10--40;99--139.
% %
% \bibitem{10.1093.biomet.asm073} Pourahmadi, Mohsen. “Cholesky Decompositions and Estimation of a Covariance Matrix: Orthogonality of Variance-Correlation Parameters.” Biometrika 94, no. 4 (2007): 1006–1013, doi: 10.1093/biomet/asm073.

% \end{thebibliography}

% \appendix
% \subsection{Código ART.m}
% \lstinputlisting{configs/code/ART.m}
% \vspace*{.5cm}

% \subsection{Código tunning.m}
% \lstinputlisting{configs/code/tunning.m}
% \vspace*{.5cm}

% \subsection{Código simulacao.m}
% \lstinputlisting{configs/code/simulacao.m}
\end{document}
