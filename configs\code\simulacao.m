% Initialize ART clustering with a similarity parameter

similarity = 0.35;
alpha = 0.4;
M = 1000;
num_of_Neurons = zeros(M,1);

for m = 1:M
    art = ART(similarity, alpha);
    % Generate synthetic data for 5 targets
    % Each row is a [Angle of Arrival, Acoustic Signature, Distance]
    % porta_avioe = [145, 5, 7000] + randn(20, 3) .* [2 0 50] + my_rand(20, 3) .* [0 1 0];   % 10 samples near target 1
    % sub_nuclear = [115, 6, 6000] + randn(20, 3) .* [2 0 50] + my_rand(20, 3) .* [0 1 0];   % 10 samples near target 2
    % destroyer_1 = [135, 3, 6800] + randn(20, 3) .* [2 0 50] + my_rand(20, 3) .* [0 1 0];   % 10 samples near target 3
    % destroyer_2 = [125, 3, 6600] + randn(20, 3) .* [2 0 50] + my_rand(20, 3) .* [0 1 0];   % 10 samples near target 4
    % destroyer_3 = [150, 3, 7000] + randn(20, 3) .* [2 0 50] + my_rand(20, 3) .* [0 1 0];   % 10 samples near target 5

    target1 = [45, 1, 1000] + randn(20, 3) .* [2 0 50] + my_rand(20, 3) .* [0 1 0];   % 10 samples near target 1
    target2 = [90, 2, 2000] + randn(20, 3) .* [2 0 50] + my_rand(20, 3) .* [0 1 0];   % 10 samples near target 2
    target3 = [135, 3, 3000] + randn(20, 3) .* [2 0 50] + my_rand(20, 3) .* [0 1 0];  % 10 samples near target 3
    target4 = [180, 4, 4000] + randn(20, 3) .* [2 0 50] + my_rand(20, 3) .* [0 1 0];  % 10 samples near target 4
    target5 = [225, 5, 5000] + randn(20, 3) .* [2 0 50] + my_rand(20, 3) .* [0 1 0];  % 10 samples near target 5

    % Combine all targets into one dataset
    % data = [porta_avioe; sub_nuclear; destroyer_1; destroyer_2; destroyer_3];
    data = [target1; target2; target3; target4; target5];

    % Shuffle the dataset to randomize order
    data = data(randperm(size(data, 1)), :);

    % Process each input through the ART algorithm
    for i = 1:size(data, 1)
        art = art.process_input(data(i, :));
    end

    % Get the number of clusters (targets) identified
    num_targets = art.get_num_targets();
    num_of_Neurons(m,1) = num_targets;
end 


%% Plot
edges = [0.5:1:10.5];
histogram(num_of_Neurons, edges);
ylabel('Freq')
xlabel('N_i')
set(gca, 'XLim', [0 10])
grid on;

% Optional: Visualize clusters (requires additional logic for grouping)

%% Functions
function value = my_rand(n,m)
    value = rand(n,m);
    value(find(value<0.025))=-1;
    value(find(value>0.975))=1;
    value(find(0.025<=value & value<=0.975))=0;
end 
