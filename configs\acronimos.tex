%%%% ABBREVIATIONS %%%%
%% Definicoes de Guerra Eletronica %%
\DeclareAcronym{ew}{
	short = EW,
	long  = Guerra Eletrônica ,
	foreign = Electronic Warfare ,
	foreign-babel = english ,
	tag = abbrev
}
\DeclareAcronym{es}{
	short = ES,
	long  = Suporte Eletrônico ,
	foreign = Electronic Suport ,
	foreign-babel = english ,
	tag = abbrev
}
\DeclareAcronym{ea}{
	short = EA,
	long  = Ataque Eletrônico ,
	foreign = Electronic Attack ,
	foreign-babel = english ,
	tag = abbrev
}
\DeclareAcronym{ep}{
	short = EP,
	long  = Proteção Eletrônica ,
	foreign = Electronic Protection ,
	foreign-babel = english ,
	tag = abbrev
}
\DeclareAcronym{eccm}{
	short = ECCM,
	long  = Contra-Contramedidas Eletrônicas ,
	foreign = Electronic Counter-Countermeasures ,
	foreign-babel = english ,
	tag = abbrev
}
\DeclareAcronym{elint}{
	short = ELINT,
	long  = Inteligência Eletrônica ,
	foreign = Electronic Inteligence ,
	foreign-babel = english ,
	tag = abbrev
}
\DeclareAcronym{pri}{
	short = PRI,
        short-plural =  s,
	long  = Intervalo de Repetição de Pulso ,
	foreign = Pulse Repetition Interval ,
	foreign-babel = english ,
        tag = abbrev
}
\DeclareAcronym{doa}{
	short = DOA,
	short-plural = s,
        long  = Direção de Chegada ,
        long-plural-form = Direções de Chegada,
	foreign = Direction of Arrival ,
	foreign-babel = english ,
        tag = abbrev
}
\DeclareAcronym{rwr}{
	short = RWR,
	long  = Receptor de Aviso-Radar ,
	foreign = Radar Warning Receiver ,
	foreign-babel = english ,
        tag = abbrev
}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

\DeclareAcronym{AOA}{
    short = AoA,
    foreign = Angle of Arrival,
    long  = Ângulo de Chegada ,
    tag = abbrev
}
\DeclareAcronym{ART}{
    short = ART,
    long  = Adaptive Resonance Theory ,
    tag = abbrev
}
\DeclareAcronym{RF}{
    short = RF ,
    long  = frequência da portadora ,
    tag = abbrev
}
\DeclareAcronym{LP}{
    short = LP ,
    long  = largura de pulso ,
    tag = abbrev
}
\DeclareAcronym{bilstm}{
    short = BiLSTM ,
    foreign = Bidirectional Long Short-Term Memory,
    long  = Memória de Tempo-Curto Longa Bidirecional ,
    tag = abbrev
}
\DeclareAcronym{iid}{
	short = iid ,
	foreign = idenpendent and identically distributed,
	long  = independente e identicamente distribuído ,
	tag = abbrev
}
\DeclareAcronym{lms}{
	short = LMS ,
	foreign = Least Mean-Squares,
	long  =  Mínimos Quadrados Médio,
	tag = abbrev
}
\DeclareAcronym{rls}{
	short = RLS ,
	foreign = Recursive Least Squares,
	long  =  Mínimos Quadrados Recursivo,
	tag = abbrev
}
\DeclareAcronym{mse}{
	short = MSE ,
	foreign = Mean Squared Error,
	long  =  Erro Quadrático Médio,
	tag = abbrev
}
\DeclareAcronym{msd}{
	short = MSD ,
	foreign = Mean Squared Deviation,
	long  =  Desvio Quadrático Médio,
	tag = abbrev
}
\DeclareAcronym{mle}{
	short = MLE ,
	foreign = Maximum Likelihood Estimator,
	long  =  Estimador de Máxima Verossimilhança,
	tag = abbrev
}
\DeclareAcronym{atc}{
	short = ATC ,
	foreign = Adapt Then Combine,
	long  =  Adaptar e depois Combinar,
	tag = abbrev
}
\DeclareAcronym{cta}{
	short = CTA ,
	foreign = Combine Then Adapt,
	long  =  Combinar e depois Adaptar,
	tag = abbrev
}
\DeclareAcronym{PDW}{
    short = PDW ,
    long  = pulse descriptor word ,
    tag = abbrev
}

%%%% SYMBOLS %%%%
% \DeclareAcronym{matrix_R}{
%     short = $\bm{R}$ ,
%     long  = covariance matrix of measurement uncertainty ,
%     tag = symbol
% }
\DeclareAcronym{h0}{
    short = $H_0$ ,
    long  = null hypothesis ,
    tag = symbol
}

\DeclareAcronym{h1}{
    short = $H_1$ ,
    long  = alternative hypothesis ,
    tag = symbol
}

\DeclareAcronym{x}{
    short = $\bm{x}[n]$ ,
    long  = current state ,
    tag = symbol
}
\DeclareAcronym{x1}{
    short = $\bm{x}[n+1]$ ,
    long  = future state ,
    tag = symbol
}
\DeclareAcronym{y}{
    short = $\bm{y}[n]$ ,
    long  = current measurement ,
    tag = symbol
}
\DeclareAcronym{x_hat}{
    short = $\bm{\hat{x}}[n]$ ,
    long  = posterior estimated state,
    tag = symbol
}
\DeclareAcronym{x_hat1}{
    short = $\bm{\hat{x}}[n+1]$ ,
    long  = prior estimated state,
    tag = symbol
}
\DeclareAcronym{nu_x}{
    short = $\bm{\nu_{x}}[n]$ ,
    long  = state noise,
    tag = symbol
}
\DeclareAcronym{nu_y}{
    short = $\bm{\nu_y}[n]$ ,
    long  = measurement noise,
    tag = symbol
}
\DeclareAcronym{A}{
    short = $\bm{A}[n]$ ,
    long  = state transition matrix,
    tag = symbol
}
\DeclareAcronym{H}{
    short = $\bm{H}[n]$ ,
    long  = measurement matrix,
    tag = symbol
}
\DeclareAcronym{I}{
    short = $\bm{I}$ ,
    long  = identity matrix,
    tag = symbol
}
\DeclareAcronym{0}{
    short = $\bm{0}$ ,
    long  = zero vector,
    tag = symbol
}
\DeclareAcronym{S}{
    short = $\bm{S}[n]$ ,
    long  = innovation covariance matrix,
    tag = symbol
}
\DeclareAcronym{P}{
    short = $\bm{P}[n|n]$ ,
    long  = posterior estimate error covariance matrix,,
    tag = symbol
}
\DeclareAcronym{P1}{
    short = $\bm{P}[n+1|n]$ ,
    long  = prior estimate error covariance matrix,
    tag = symbol
}
\DeclareAcronym{R}{
    short = $\bm{R}[n]$ ,
    long  = measurement noise covariance matrix,
    tag = symbol
}
\DeclareAcronym{K}{
    short = $\bm{K}[n]$ ,
    long  = Kalman gain matrix,
    tag = symbol
}
\DeclareAcronym{alpha}{
    short = $\bm{\alpha}[n]$ ,
    long  = measurement innovation,
    tag = symbol
}
\DeclareAcronym{Q}{
    short = $\bm{Q}[n]$ ,
    long  = state noise covariance matrix,
    tag = symbol
}
\DeclareAcronym{r}{
    short = $r[n]$ ,
    long  = range,
    tag = symbol
}
\DeclareAcronym{phi}{
    short = $\phi[n]$ ,
    long  = bearing,
    tag = symbol
}
\DeclareAcronym{nuyl}{
    short = $\bm{\nu_y'}[n]$ ,
    long  = correlated measurement noise,
    tag = symbol
}

