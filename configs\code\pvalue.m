mu1 = 20;
mu2 = 19;
mu3 = 18;
sd = 2;
runs = 1e4;
w1 = sd*randn(4,runs)+mu1;
w2 = sd*randn(4,runs)+mu2;
w3 = sd*randn(4,runs)+mu3;

for n = 1:runs
    [H1(n), p1(n),ci,stats1(n)] = ttest(w1(:,n), mu1, 'Tail', 'left', 'alpha', 0.05);
    [H2(n), p2(n),ci,stats2(n)] = ttest(w2(:,n), mu1, 'Tail', 'left', 'alpha', 0.05);
    [H3(n), p3(n),ci,stats3(n)] = ttest(w3(:,n), mu1, 'Tail', 'left', 'alpha', 0.05);
end
%%
figure(1)
subplot(3,1,1)
h1 = histogram(p1)
h1.Normalization = 'probability';
h1.BinWidth = 0.05;
ylabel('Pr[p_1 <= bin]')

subplot(3,1,2)
h2 = histogram(p2)
h2.Normalization = 'probability';
h2.BinWidth = 0.05;
ylabel('Pr[p_2 <= bin]')

subplot(3,1,3)
h3 = histogram(p3)
h3.Normalization = 'probability';
h3.BinWidth = 0.05;
ylabel('Pr[p_3 <= bin]')
xlabel('P-value')