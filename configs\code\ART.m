classdef ART
    properties
        similarity      % Vigilance parameter for cluster matching
        weight_matrix   % Matrix to store cluster weights
        num_clusters    % Number of clusters
        coef_update     % Alpha to weighted updates
        sigma_aoa       % Standart Deviation AoA
        sigma_r         % Standart Deviation Range
    end

    methods
        function obj = ART(similarity, coef_update)
            % Constructor to initialize the ART network
            obj.similarity = similarity;
            obj.coef_update = coef_update;
            obj.weight_matrix = [];
            obj.num_clusters = 0;
            obj.sigma_r = 50;
            obj.sigma_aoa = 3;
        end

        function obj = process_input(obj, input_vec)
            % Process a single input vector and assign it to a cluster
            if obj.num_clusters == 0
                % Initialize the first cluster
                obj.num_clusters = 1;
                obj.weight_matrix = input_vec;
            else
                % Compute similarity with existing clusters
                similarities = obj.calc_similarities(input_vec);
                [max_sim, best_neuron] = max(similarities);

                if max_sim >= obj.similarity
                    % Update the matched cluster prototype
                    obj.weight_matrix(best_neuron, :) = (1-obj.coef_update) * obj.weight_matrix(best_neuron, :) + obj.coef_update * input_vec;
                else
                    % Create a new cluster
                    obj.num_clusters = obj.num_clusters + 1;
                    obj.weight_matrix = [obj.weight_matrix; input_vec];
                end
            end
        end

        function similarities = calc_similarities(obj, input_vec)
            % Calculate similarity of input vector to all cluster prototypes
            % num_clusters = obj.get_num_clusters();
            similarities = zeros(1, obj.num_clusters);
            for i = 1:obj.num_clusters
                prototype = obj.weight_matrix(i, :);
                % Compute similarity (normalized dot product)
                simi_AoA = exp(-abs(prototype(1) - input_vec(1))^2 / (2*obj.sigma_aoa^2));
                simi_class = exp(-abs(prototype(2) - input_vec(2)) / 2);
                simi_dist = exp(-abs(prototype(3) - input_vec(3))^2 / (2*obj.sigma_r^2));
                similarities(i) = sum([simi_AoA, simi_class, simi_dist].*[.35 .3 .35]);
            end
            % disp(similarities)
        end

        function cluster_index = classify(obj, input_vec)
            % Classify an input vector by finding the best matching cluster
            similarities = obj.calc_similarities(input_vec);
            [max_sim, cluster_index] = max(similarities);
            if max_sim < obj.similarity
                cluster_index = -1;      % Indicates no match
            end
        end

        function num_targets = get_num_targets(obj)
            % Return the number of identified clusters (targets)
            num_targets = obj.num_clusters;
        end
    end
    % methods (Access = private)
    %     function num_clusters = get_num_clusters(obj)
    %         num_clusters = obj.num_clusters;
    %         % num_clusters = size(obj.weight_matrix, 1);
    %     end
    % end
end
