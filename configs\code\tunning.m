% Initialize ART clustering with a similarity parameter

similarity = 0:0.01:1;
alpha = 0.4;
N = 101;
M = 100;
num_of_Neurons = zeros(M,N);
for n = 1:N
    for m = 1:M
        art = ART(similarity(n), alpha);
        % Generate synthetic data for 5 targets
        % Each row is a [Angle of Arrival, Acoustic Signature, Distance]
        target1 = [45, 1, 1000] + randn(20, 3) .* [2 0 50] + my_rand(20, 3) .* [0 1 0];   % 10 samples near target 1
        target2 = [90, 2, 2000] + randn(20, 3) .* [2 0 50] + my_rand(20, 3) .* [0 1 0];   % 10 samples near target 2
        target3 = [135, 3, 3000] + randn(20, 3) .* [2 0 50] + my_rand(20, 3) .* [0 1 0];  % 10 samples near target 3
        target4 = [180, 4, 4000] + randn(20, 3) .* [2 0 50] + my_rand(20, 3) .* [0 1 0];  % 10 samples near target 4
        target5 = [225, 5, 5000] + randn(20, 3) .* [2 0 50] + my_rand(20, 3) .* [0 1 0];  % 10 samples near target 5
    
        % Combine all targets into one dataset
        data = [target1; target2; target3; target4; target5];
    
        % Shuffle the dataset to randomize order
        data = data(randperm(size(data, 1)), :);
    
        % Process each input through the ART algorithm
        for i = 1:size(data, 1)
            art = art.process_input(data(i, :));
        end
    
        % Get the number of clusters (targets) identified
        num_targets = art.get_num_targets();
        num_of_Neurons(m,n) = num_targets;
    end 
end

%% Plot
avg_num_of_Neurons = mean(num_of_Neurons);
std_num_of_Neurons = std(num_of_Neurons);
errorbar(similarity, avg_num_of_Neurons, std_num_of_Neurons);
ylabel('N_i')
xlabel('u_0')
grid on;

% Optional: Visualize clusters (requires additional logic for grouping)

%% Functions
function value = my_rand(n,m)
    value = rand(n,m);
    value(find(value<0.025))=-1;
    value(find(value>0.975))=1;
    value(find(0.025<=value & value<=0.975))=0;
end 
